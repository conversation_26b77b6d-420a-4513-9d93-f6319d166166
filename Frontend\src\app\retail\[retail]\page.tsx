import TabChallenges from '@components/TabChallenges';
import seoSchema from '@utils/seoSchema';
import { notFound } from 'next/navigation';
import fetchFromStrapi from '@utils/fetchFromStrapi';
import RichResults from '@components/RichResults';

export async function generateStaticParams() {
  const res = await fetchFromStrapi('retail-pages');
  return (
    res?.data?.map(item => ({
      retail: item.attributes.slug,
    })) || []
  );
}

export async function getFormData() {
  return await fetchFromStrapi(
    'form',
    'populate=form.formFields&populate=form.button',
  );
}

export async function getAwardsData() {
  return await fetchFromStrapi('award', 'populate=awards.awards_box.image');
}

export async function getTrustedPartnersData() {
  return await fetchFromStrapi(
    'trusted-partner',
    'populate=trustedPartner.title&populate=trustedPartner.partnersLogo.images',
  );
}

export async function fetchRetailPage(slug: string) {
  const query = `filters[slug][$eq]=${slug}&populate=hero_section.image,hero_section.mobile_image&populate=tab_section&populate=retail_components,retail_components.challenges_and_solutions.box,retail_components.what_service_we_are_offering.L2ServicesCard.on_hover_bg_image,retail_components.cta,retail_components.case_study_cards.case_study_relation.hero_section.global_services,retail_components.case_study_cards.case_study_relation.preview.preview_background_image,retail_components.why_choose_maruti_techlabs.whyChooseMtlCards,retail_components.cta_other,retail_components.tech_stack.tab,retail_components.clutch_reviews.review_image,retail_components.insights.blogs.heroSection_image,retail_components.insights.circular_text_image,retail_components.faq.faq_items,seo.schema`;

  return await fetchFromStrapi('retail-pages', query);
}

export async function generateMetadata({
  params,
}: {
  params: { retail: string };
}) {
  const { retail: slug } = params;
  const queryString = `filters[slug][$eq]=${slug}&populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords&populate=seo.schema`;
  const seoFetchedData = await fetchFromStrapi('retail-pages', queryString);

  const seoData = seoFetchedData?.data?.[0]?.attributes?.seo;
  return seoSchema(seoData);
}
export default async function RetailPage({
  params,
}: {
  params: { retail: string };
}) {
  const { retail } = params;
  const retailData = await fetchRetailPage(retail);
  const formData = await getFormData();
  const awardsData = await getAwardsData();
  const trustedPartnersData = await getTrustedPartnersData();

  // Check if Retail Data service page data exists, otherwise return 404
  if (!retailData?.data || retailData?.data.length === 0) {
    notFound();
  }

  return (
    <>
      {retailData?.data[0]?.attributes?.seo && (
        <RichResults data={retailData?.data[0]?.attributes?.seo} />
      )}
      {retailData && (
        <TabChallenges
          tabChallengesData={retailData?.data[0]?.attributes}
          variant="retail"
          awardsData={awardsData?.data?.attributes?.awards}
          formData={formData?.data?.attributes?.form}
          trustedPartnersData={
            trustedPartnersData?.data?.attributes?.trustedPartner
          }
        />
      )}
    </>
  );
}
