# ------------------------
# <PERSON><PERSON><PERSON>t OAC
# ------------------------
resource "aws_cloudfront_origin_access_control" "this" {
  name                              = "mtl-site-oac"
  description                       = "CloudFront OAC for secure access to S3"
  origin_access_control_origin_type = "s3"
  signing_behavior                  = "always"
  signing_protocol                  = "sigv4"
}

# ------------------------
# CloudFront Response Header Policy (Security Headers)
# ------------------------
resource "aws_cloudfront_response_headers_policy" "mtl_security_headers" {
  name = "MTL-Security-Headers-Policy"

  security_headers_config {
    strict_transport_security {
      access_control_max_age_sec = 31536000
      include_subdomains         = true
      preload                    = true
      override                   = true
    }

    frame_options {
      frame_option = "DENY"
      override     = true
    }

    content_type_options {
      override = true
    }

    content_security_policy {
      content_security_policy = "img-src 'self' data: storage.googleapis.com cdn-gcp.marutitech.com cdn-gcp.new.marutitech.com cdn.marutitech.com px.ads.linkedin.com i.ytimg.com c.clarity.ms b.6sc.co track.hubspot.com; frame-src 'self' data: www.youtube.com www.googletagmanager.com; object-src 'none';"
      override                = true
    }

  }
  custom_headers_config {
    items {
      header   = "cross_origin_opener_policy"
      override = true
      value    = "same-origin"
    }

  }
}

data "aws_cloudfront_cache_policy" "caching_optimized" {
  name = "Managed-CachingOptimized"
}

data "aws_cloudfront_cache_policy" "caching_disabled" {
  name = "Managed-CachingDisabled"
}

resource "aws_cloudfront_origin_request_policy" "api_gateway_origin_policy" {
  name = "API-Gateway-Origin-Request-Policy"

  headers_config {
    header_behavior = "none"
  }

  query_strings_config {
    query_string_behavior = "all"
  }

  cookies_config {
    cookie_behavior = "none"
  }
}

# ------------------------
# CloudFront Distribution
# ------------------------
resource "aws_cloudfront_distribution" "mtl-cloudfront" {
  enabled             = true
  default_root_object = "index.html"

  custom_error_response {
    error_code         = 403
    response_code      = 404
    response_page_path = "/404.html"
  }

  custom_error_response {
    error_code         = 404
    response_code      = 404
    response_page_path = "/404.html"
  }

  origin {
    domain_name              = aws_s3_bucket.static_site.bucket_regional_domain_name
    origin_id                = "s3-origin-${aws_s3_bucket.static_site.id}"
    origin_access_control_id = aws_cloudfront_origin_access_control.this.id
  }

  origin {
    domain_name = "${aws_api_gateway_rest_api.maruti_site_api.id}.execute-api.${var.aws_region}.amazonaws.com"
    origin_id   = "api-gateway-origin"

    custom_origin_config {
      http_port              = 80
      https_port             = 443
      origin_protocol_policy = "https-only"
      origin_ssl_protocols   = ["TLSv1.2"]
    }

  }

  ordered_cache_behavior {
    path_pattern     = "/sendToSlack"
    target_origin_id = "api-gateway-origin"

    allowed_methods = ["GET", "HEAD", "OPTIONS", "PUT", "PATCH", "POST", "DELETE"]
    cached_methods  = ["GET", "HEAD"]

    viewer_protocol_policy     = "redirect-to-https"
    cache_policy_id            = data.aws_cloudfront_cache_policy.caching_disabled.id
    origin_request_policy_id   = aws_cloudfront_origin_request_policy.api_gateway_origin_policy.id
    response_headers_policy_id = aws_cloudfront_response_headers_policy.mtl_security_headers.id
  }

  default_cache_behavior {
    target_origin_id = "s3-origin-${aws_s3_bucket.static_site.id}"
    allowed_methods  = ["GET", "HEAD"]
    cached_methods   = ["GET", "HEAD"]

    viewer_protocol_policy     = "redirect-to-https"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.mtl_security_headers.id

    function_association {
      event_type   = "viewer-request"
      function_arn = aws_cloudfront_function.mtl-site-dev.arn
    }

    forwarded_values {
      headers      = []
      query_string = false
      cookies {
        forward = "none"
      }
    }
  }

  price_class = "PriceClass_100"

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  aliases = ["test.marutitech.com"]

  viewer_certificate {
    acm_certificate_arn      = "arn:aws:acm:us-east-1:851725323009:certificate/f927a945-cba2-4199-a574-82e201033a8c"
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.2_2021"
  }
  tags = var.common_tags
}

# ------------------------
# CloudFront Function
# ------------------------
resource "aws_cloudfront_function" "mtl-site-dev" {
  name    = "mtl-site-${var.environment}"
  runtime = "cloudfront-js-1.0"
  comment = "Appends index.html to URIs ending in /"

  publish = true

  code = <<EOT
function handler(event) {
  var request=event.request;
  var uri=request.uri.toLowerCase();      
  var redirectMap={
    "/hire-dedicated-development-team" : "/services/staff-augmentation/hire-dedicated-development-teams",
    "/it-outsourcing-services" : "/services/staff-augmentation/it-outsourcing",
    "/agile-software-development-services": "/services/staff-augmentation/hire-agile-developers",
    "/hire-python-developers" : '/services/staff-augmentation/hire-python-developers',
    "/hire-reactjs-developers" : '/services/staff-augmentation/hire-react-developers',
    "/hire-angularjs-developers" : '/services/staff-augmentation/hire-angular-developers',
    "/hire-asp-net-developers" : '/services/staff-augmentation/hire-dot-net-developers',
    "/hire-node-js-developers"  : '/services/staff-augmentation/hire-node-js-developers',
    "/hire-mobile-app-developers"  : '/services/staff-augmentation/hire-mobile-app-developers',
    "/cto-as-a-service": '/services/staff-augmentation/virtual-cto-services',
    "/product-strategy-consulting-services" : '/services/technology-advisory/product-strategy',
    '/product-management-consulting-services': '/services/technology-advisory/product-management-consulting',
    '/digital-transformation-services': '/services/technology-advisory/digital-transformation-consulting',
    '/enterprise-application-modernization-services': '/services/technology-advisory/enterprise-application-modernization',
    '/software-audit': '/services/technology-advisory/code-audit',
    '/risk-and-compliance': '/services/technology-advisory/risk-and-compliance-services',
    '/google-cloud-development-services':'/services/technology-advisory/google-cloud-development',
    '/bot-development-services':'/services/interactive-experience/chatbot-development',
    '/robotic-process-automation-services':'/services/interactive-experience/robotic-process-automation',
    '/saas-application-development-services': '/services/software-product-engineering/saas-application-development',
    '/web-application-development-services': '/services/software-product-engineering/web-application-development',
    '/mobile-app-development-services': '/services/software-product-engineering/mobile-app-development',
    '/low-code-no-code-services': '/services/software-product-engineering/low-code-no-code-development ',
    '/ui-test-automation': '/services/quality-engineering/ui-test-automation',
    '/performance-testing-services': '/services/quality-engineering/performance-testing',
    '/security-testing-services': '/services/quality-engineering/security-testing',
    '/functional-testing-services': '/services/quality-engineering/functional-testing',
    '/infrastructure-support-services': '/services/maintenance-and-support/infrastructure-managed-services',
    '/application-maintenance-services': '/services/maintenance-and-support/application-support-services',
    '/cloud-native-application-development': '/services/cloud-application-development/cloud-native-application-development',
    '/microservices-consulting-services': '/services/cloud-application-development/microservices-consulting',
    '/serverless-app-development-services' : '/services/cloud-application-development/serverless-app-development',
    '/cloud-migration-services': '/services/cloud-application-development/cloud-migration-consulting',
    '/cloud-consulting-services': '/services/cloud-application-development/cloud-computing-consulting',
    '/cloud-security-services': '/services/cloud-application-development/cloud-security-services',
    '/cloud-infrastructure-management-services': '/services/devops-consulting/cloud-infrastructure-services',
    '/devops-ci-cd-services': '/services/devops-consulting/ci-cd-solutions',
    '/containerization-services': '/services/devops-consulting/containerization-services',
    '/infrastructure-as-code-iac-services': '/services/devops-consulting/infrastructure-as-code',
    '/system-integration-services': '/services/devops-consulting/systems-integration',
    '/data-engineering-services': '/services/data-analytics-consulting/data-engineering',
    '/business-intelligence-consulting-services': '/services/data-analytics-consulting/business-intelligence-consulting',
    '/machine-learning-services': '/services/artificial-intelligence-consulting/machine-learning',
    '/natural-language-processing-services': '/services/artificial-intelligence-consulting/natural-language-processing',
    '/computer-vision-services': '/services/artificial-intelligence-consulting/computer-vision',
    '/user-research-testing': '/services/ui-ux-design-and-development/user-research-and-testing',
    '/software-prototyping-services': '/services/ui-ux-design-and-development/rapid-prototyping-software',
    "/software-product-development-services":"/services/software-product-engineering",
    "/quality-engineering-services":"/services/quality-engineering",
    "/cloud-application-development-services":"/services/cloud-application-development",
    "/devops-consulting-services":"/services/devops-consulting",
    "/data-analytics-services": "/services/data-analytics-consulting",
    "/artificial-intelligence-services":"/services/artificial-intelligence-consulting",
    "/ui-ux-design-and-development-services":"/services/ui-ux-design-and-development",
    "/business-technology-consulting":"/services/technology-advisory",
    "/it-staff-augmentation-services":"/services/staff-augmentation",
    "/free-consultation-page": "/contact-us",
    "/case-study/aws-cloud-cost-optimization":"/case-study",
    "/products/wotnot": "",
    "/home":"",
    "/software-product-development-services-abm":"/services/software-product-engineering",
    "/abm/product-dev-campaign":"/services/software-product-engineering",
    "/abm/devops-campaign": "/services/cloud-application-development",
    "/partners/aws/solutions-abm": "/partners/aws/solutions",
    "/insights" : "/resources",
    "/white-paper": "",
    "/artificial-intelligence-in-insurance" : '/top-ai-insurance-use-cases', 
    "/bot-development" : '/services/interactive-experience/chatbot-development',
    "/trends-chatbot" : '/chatbot-trends',
    "/traits-good-chatbot" : '/complete-guide-chatbots',
    "/chatbots-and-service-industry" : '/complete-guide-chatbots',
    "/7-reasons-why-business-needs-chatbot" : '/complete-guide-chatbots',
    '/heres-need-know-chatbots': '/complete-guide-chatbots',
    '/design-chatbot-conversation':'/complete-guide-chatbots',
    '/what-chatbots-can-do-for-e-commerce-industry':'/complete-guide-chatbots',
     '/ai-in-the-insurance-industry' : '/ai-insurance-implementation-challenges-solutions',
    '/what-is-a-citizen-developer' : '/citizen-developer-framework' ,
    '/ways-ai-transforming-finance':'/artificial-intelligence-and-machine-learning',
    '/case-study/chatbot-solution-artificial-intelligence' :'/case-study/sms-chatbot',
    '/case-study/ai-medical-record-summarization-tool' :'/case-study/medical-record-processing-using-nlp',
    '/business-process-automation-bot-solution': '/case-study/scheduling-chatbot',
    '/product-dev-services-designrush' : '/services/software-product-engineering',
    '/product-development-manifest' : '/services/software-product-engineering',
    "/mobile-application-development-trends":  '/7-trends-of-mobile-application-development',
    '/cognitive-computing-features-scope' : '/cognitive-computing-features-scope-limitations',
    " /predictive-analytics-modelsalgorithms" :'/predictive-analytics-models-algorithms',
    "/campaign/retail-data-engineering-and-analytics":'/retail',
    '/retail-data-engineering-and-analytics':'/retail'};
  var rewriteMap = {
    "/healthcare": "/industry/healthcare",
    "/legal":"/industry/legal",
    "/retail":"/retail/retail",
    "/insurance":"/industry/insurance",
    "/cloud-consulting-solution": "/cloud-consulting/cloud-consulting-solution",
    "/data-visualization-services": "/cloud-consulting/data-visualization-services",
     "/top-ai-insurance-use-cases" : "/blog/top-ai-insurance-use-cases",
     "/chatbot-trends" : "/blog/chatbot-trends",
     "/complete-guide-chatbots" : "/blog/complete-guide-chatbots",
     "/citizen-developer-framework" : "/blog/citizen-developer-framework",
     "/ai-insurance-implementation-challenges-solutions" : "/blog/ai-insurance-implementation-challenges-solutions",
     "/artificial-intelligence-and-machine-learning" : "/blog/artificial-intelligence-and-machine-learning",
     '/7-trends-of-mobile-application-development' :'/blog/7-trends-of-mobile-application-development',
     '/cognitive-computing-features-scope-limitations' : '/blog/cognitive-computing-features-scope-limitations',
     "/predictive-analytics-modelsalgorithms" :'/blog/predictive-analytics-models-algorithms'};
  // Normalize the URI
  var key = uri.endsWith("/") ? uri.slice(0, -1) : uri;

  // Check redirect map
  if (redirectMap.hasOwnProperty(key)) {
    var host = request.headers.host.value;
    var redirect = "https://" + host + redirectMap[key] + "/";
    return {
      statusCode: 301,
      headers: { location: { value: redirect } },
    };
  }

  // Check rewrite map
  if (rewriteMap.hasOwnProperty(key)) {
    request.uri = rewriteMap[key];
    if (request.uri.endsWith("/")) {
      request.uri += "index.html";
    } else if (!request.uri.endsWith(".html")) {
      request.uri += "/index.html";
    }
    return request;
  }

  // Fallback logic for trailing slash URIs
  if (request.uri.endsWith("/")) {
    request.uri += "index.html";
  }

  return request;
  }
EOT
}

# ------------------------
# CloudFront OAC  for maruti-site-cdn
# ------------------------
resource "aws_cloudfront_origin_access_control" "oac" {
  name                              = "cdn-oac-maruti-site-cdn"
  description                       = "OAC for maruti-site-cdn"
  origin_access_control_origin_type = "s3"
  signing_behavior                  = "always"
  signing_protocol                  = "sigv4"
}

# -----------------------------
# Response Headers Policy (set Cache-Control)
# -----------------------------
resource "aws_cloudfront_response_headers_policy" "static_assets" {
  name = "static-assets-headers-policy"

  custom_headers_config {
    items {
      header   = "Cache-Control"
      override = true
      value    = "public, max-age=9999999999, must-revalidate"
    }
  }
}

# ------------------------
# CloudFront Distribution
# ------------------------
resource "aws_cloudfront_distribution" "cdn" {
  enabled             = true
  default_root_object = "index.html"

  origin {
    domain_name              = aws_s3_bucket.maruti_site_cdn.bucket_regional_domain_name
    origin_id                = "s3-origin-${aws_s3_bucket.maruti_site_cdn.id}"
    origin_access_control_id = aws_cloudfront_origin_access_control.oac.id
  }

  default_cache_behavior {
    target_origin_id = "s3-origin-${aws_s3_bucket.maruti_site_cdn.id}"
    allowed_methods  = ["GET", "HEAD"]
    cached_methods   = ["GET", "HEAD"]

    viewer_protocol_policy = "redirect-to-https"

    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }
  }
  dynamic "ordered_cache_behavior" {
    for_each = toset(var.image_extensions)
    content {
      path_pattern           = "/*.${ordered_cache_behavior.key}"
      target_origin_id       = "s3-origin-maruti-site-cdn"
      viewer_protocol_policy = "redirect-to-https"

      allowed_methods = ["GET", "HEAD"]
      cached_methods  = ["GET", "HEAD"]

      compress = true

      cache_policy_id            = data.aws_cloudfront_cache_policy.caching_optimized.id
      response_headers_policy_id = aws_cloudfront_response_headers_policy.static_assets.id
    }
  }
  price_class = "PriceClass_100"

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    cloudfront_default_certificate = true
  }

  tags = {
    Name = "Maruti Site CDN"
  }
}