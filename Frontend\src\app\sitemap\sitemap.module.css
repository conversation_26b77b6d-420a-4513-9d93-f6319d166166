@value variables: "@styles/variables.module.css";
@value colorBlack, colorWhite, gray300, gray400, gray600, gray700, gray800, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-xl-1440, breakpoint-lg, breakpoint-md, breakpoint-sm from breakpoints;

.container {
  min-height: 100vh;
  padding: 80px 124px;
  background-color: colorWhite;

  @media (max-width: breakpoint-xl-1440) {
    padding: 80px 60px;
  }

  @media (max-width: breakpoint-md) {
    padding: 40px 32px;
  }

  @media (max-width: breakpoint-sm) {
    padding: 40px 16px;
  }
}

.header {
  text-align: center;
  margin-bottom: 60px;
}

.title {
  font-size: 52px;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -1.04px;
  color: colorBlack;
  margin-bottom: 16px;

  @media (max-width: breakpoint-md) {
    font-size: 40px;
  }

  @media (max-width: breakpoint-sm) {
    font-size: 32px;
  }
}

.description {
  font-size: 20px;
  line-height: 150%;
  color: gray800;
  max-width: 600px;
  margin: 0 auto 24px;

  @media (max-width: breakpoint-sm) {
    font-size: 18px;
  }
}

.sitemapContent {
  max-width: 1200px;
  margin: 0 auto;
}

.categorySection {
  margin-bottom: 40px;

  @media (max-width: breakpoint-sm) {
    margin-bottom: 32px;
  }
}

.categoryTitle {
  font-size: 24px;
  font-weight: 600;
  color: colorBlack;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid gray400;

  @media (max-width: breakpoint-sm) {
    font-size: 20px;
    margin-bottom: 12px;
  }
}

.pageList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 8px 24px;

  @media (max-width: breakpoint-md) {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 6px 20px;
  }

  @media (max-width: breakpoint-sm) {
    grid-template-columns: 1fr;
    gap: 4px;
  }
}

.pageItem {
  position: relative;
  padding-left: 20px;
}

.pageItem::before {
  content: '•';
  position: absolute;
  left: 0;
  font-weight: bold;
  font-size: 16px;
}

.pageLink {
  display: inline-block;
  color: colorBlack;
  text-decoration: none;
  font-size: 16px;
  line-height: 1.5;
  transition: color 0.2s ease;

  @media (max-width: breakpoint-sm) {
    font-size: 15px;
  }
}

.pageLink:hover {
  color: brandColorThree;
  text-decoration: none;
}

.footer {
  text-align: center;
  padding-top: 40px;
  margin-top: 60px;
  border-top: 1px solid gray400;

  @media (max-width: breakpoint-sm) {
    margin-top: 40px;
    padding-top: 24px;
  }
}

.lastUpdated {
  font-size: 14px;
  color: gray600;
  margin: 0;
}