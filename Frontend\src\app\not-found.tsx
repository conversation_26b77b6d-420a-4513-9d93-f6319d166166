'use client';
import React from 'react';
import Button from '@components/Button';
import style from './not-found.module.css';
import Image from 'next/image';
import { Container } from 'react-bootstrap';
import { useRouter } from 'next/navigation';

export default function NotFound() {
  const router = useRouter();
  return (
    <Container fluid className={style.container}>
      <Image
        src={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/cuate_1745166738.svg`}
        className={style.image}
        alt="404"
        width={666}
        height={666}
      />
      <Button
        className={style.button}
        type="button"
        onClick={() => {
          router.push('/');
        }}
      >
        <div className={style.backToHomeButton}>
          <Image
            src={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/arrow_left_1c4ee29f6b.svg`}
            width={24}
            height={24}
            alt="arrow"
          />
          <span>Back To Home</span>
        </div>
      </Button>
    </Container>
  );
}
