/**
 * @fileoverview AI Readiness Assessment form submission handler
 * This AWS Lambda function handles AI readiness assessment form submissions by:
 * 1. Parsing comprehensive AI readiness form data from the event
 * 2. Sending assessment data to HubSpot CRM with all scoring metrics
 * 3. Sending detailed assessment report email via SendGrid
 * 4. Sending notification to Slack with assessment summary
 *
 * Configuration is loaded from AWS SSM Parameter Store with fallback to environment variables.
 *
 * <AUTHOR> Team
 * @version 2.0.0
 */

import sendDataToHubspot from "../../common/sendDataToHubSpot.mjs";
import sendDataToSendGrid from "../../common/sendDataToSendGrid.mjs";
import currentTimestamp from "../../common/currentTimestamp.mjs";
import sendToSlack from "../../common/sendDataToSlack.mjs";
import { getConfigValue } from "../../common/ssmConfig.mjs";

/**
 * AWS Lambda handler for AI readiness assessment form submissions
 *
 * @async
 * @function handler
 * @param {Object} event - AWS Lambda event object
 * @param {string} event.body - JSON string containing AI readiness form data
 * @param {Object} event.headers - HTTP headers from the request
 * @param {Object} event.requestContext - AWS Lambda request context
 * @returns {Promise<Object>} Lambda response object with statusCode and body
 *
 * @example
 * // Example event.body structure (includes all contact fields plus AI-specific fields):
 * {
 *   "firstName": "Jane",
 *   "lastName": "Smith",
 *   "emailAddress": "<EMAIL>",
 *   "companyName": "Tech Corp",
 *   "do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_": "Yes, we have clear objectives",
 *   "strategy___leadership": "85",
 *   "budget___resources": "70",
 *   "talent___skills": "60",
 *   "data_readiness___infrastructure": "75",
 *   "impact_evaliation": "80",
 *   "execution___monitoring": "65",
 *   "average_of_all_score": "72.5"
 * }
 *
 * @example
 * // Success response:
 * {
 *   "statusCode": 200,
 *   "body": "{\"message\":\"Form submitted successfully.\",\"hubspotResponse\":\"AIReadiness form data sent to HubSpot successfully.\"}"
 * }
 */
export const handler = async (event) => {
  try {
    const form_data = JSON.parse(event.body);

    const formFields = [
      { name: "firstname", value: form_data?.firstName ?? "" },
      { name: "lastname", value: form_data?.lastName ?? "" },
      { name: "email", value: form_data?.emailAddress ?? "" },
      { name: "company", value: form_data?.companyName },
      { name: "phone", value: form_data?.phoneNumber ?? "" },
      { name: "city", value: form_data?.city ?? "" },
      { name: "country", value: form_data?.country ?? "" },
      { name: "ip_address", value: form_data?.ip_address ?? "" },
      { name: "ga_4_userid", value: form_data?.ga_4_userid },
      { name: "clarity_link", value: form_data?.clarity ?? "" },
      { name: "source", value: form_data?.secondary_source ?? "HomePage" },
      { name: "source_url", value: form_data?.url ?? "" },
      { name: "utm_campaign", value: form_data?.utm_campaign ?? "" },
      { name: "utm_source", value: form_data?.utm_source ?? "" },
      { name: "utm_medium", value: form_data?.utm_medium ?? "" },
      { name: "referrer", value: form_data?.referrer ?? "" },
      { name: "consent", value: form_data?.consent ?? "" },
      {
        name: "do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_",
        value:
          form_data?.do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_ ??
          "",
      },
      {
        name: "how_receptive_is_your_leadership_team_to_embracing_the_changes_brought_about_by_ai_",
        value:
          form_data?.how_receptive_is_your_leadership_team_to_embracing_the_changes_brought_about_by_ai_ ??
          "",
      },
      {
        name: "do_you_have_budget_allocated_for_your_ai_project_",
        value:
          form_data?.do_you_have_budget_allocated_for_your_ai_project_ ?? "",
      },
      {
        name: "do_you_have_a_robust_data_infrastructure_for_storage__retrieval__and_processing_",
        value:
          form_data?.do_you_have_a_robust_data_infrastructure_for_storage__retrieval__and_processing_ ??
          "",
      },
      {
        name: "which_of_the_below_db_tools_do_you_currently_use_",
        value:
          form_data?.which_of_the_below_db_tools_do_you_currently_use_ ?? "",
      },
      {
        name: "is_the_relevant_data_for_the_ai_project_available_and_accessible_",
        value:
          form_data?.is_the_relevant_data_for_the_ai_project_available_and_accessible_ ??
          "",
      },
      {
        name: "do_you_have_access_to_necessary_computing_resources__cpu__gpu__cloud_services__",
        value:
          form_data?.do_you_have_access_to_necessary_computing_resources__cpu__gpu__cloud_services__ ??
          "",
      },
      {
        name: "how_would_you_rate_your_organization_s_current_it_infrastructure_in_terms_of_scalability_and_flexib",
        value:
          form_data?.how_would_you_rate_your_organization_s_current_it_infrastructure_in_terms_of_scalability_and_flexib ??
          "",
      },
      {
        name: "does_the_team_have_the_expertise_in_data_science__machine_learning__and_ai_",
        value:
          form_data?.does_the_team_have_the_expertise_in_data_science__machine_learning__and_ai_ ??
          "",
      },
      {
        name: "do_you_have_systems_in_place_to_monitor_the_performance_and_accuracy_of_ai_models_post_deployment_",
        value:
          form_data?.do_you_have_systems_in_place_to_monitor_the_performance_and_accuracy_of_ai_models_post_deployment_ ??
          "",
      },
      {
        name: "do_you_have_risk_management_strategies_in_place_for_the_ai_project_",
        value:
          form_data?.do_you_have_risk_management_strategies_in_place_for_the_ai_project_ ??
          "",
      },
      {
        name: "do_you_have_a_process_in_place_to_measure_the_impact_of_the_deployment_of_ai___ai_powered_solutions",
        value:
          form_data?.do_you_have_a_process_in_place_to_measure_the_impact_of_the_deployment_of_ai___ai_powered_solutions ??
          "",
      },
      {
        name: "strategy___leadership",
        value: form_data?.strategy___leadership ?? "",
      },
      {
        name: "talent___skills",
        value: form_data?.talent___skills ?? "",
      },
      {
        name: "data_readiness___infrastructure",
        value: form_data?.data_readiness___infrastructure ?? "",
      },
      {
        name: "impact_evaliation",
        value: form_data?.impact_evaliation ?? "",
      },
      {
        name: "execution___monitoring",
        value: form_data?.execution___monitoring ?? "",
      },
      {
        name: "average_of_all_score",
        value: form_data?.average_of_all_score ?? "",
      },
    ];

    // Get configuration values from SSM
    const [hubspotApiKey, hubspotFormGuid] = await Promise.all([
      getConfigValue("HUBSPOT_API_KEY"),
      getConfigValue("HUBSPOT_AI_READINESS_FORM_GUID"),
    ]);

    const payload = {
      fields: formFields,
      context: { pageUri: form_data?.url },
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${hubspotApiKey}`,
      },
    };

    try {
      const hubspotResponse = await sendDataToHubspot(
        form_data?.secondary_source,
        payload,
        hubspotFormGuid
      );

      if (hubspotResponse?.status === 200 || hubspotResponse?.status === 201) {
        // Get email configuration from SSM
        const [mailTo, mailFrom, emailTemplateId] = await Promise.all([
          getConfigValue("MAIL_TO"),
          getConfigValue("MAIL_FROM"),
          getConfigValue("SENDGRID_AI_READINESS_FORM_TEMPLATE_ID"),
        ]);

        const emailRes = await sendDataToSendGrid(
          mailTo,
          mailFrom,
          form_data?.emailAddress,
          emailTemplateId,
          form_data
        );

        // Send Data to success Slack channel (webhook URL will be determined by sendToSlack)
        await sendToSlack(form_data);

        console.log(currentTimestamp());
        console.log("Lead Data", form_data);
        console.log("HubSpot Response", hubspotResponse);
        console.log("SendGrid Email Response", emailRes);

        return {
          statusCode: 200,
          headers: {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
            "Access-Control-Allow-Methods": "POST,OPTIONS"
          },
          body: JSON.stringify({
            message: "Form submitted successfully.",
            hubspotResponse: hubspotResponse.message,
          }),
        };
      } else {
        console.error("HubSpot Error:", hubspotResponse);

        let formLeadData = form_data;
        formLeadData.page_name = form_data?.secondary_source;
        formLeadData.failed_source = "Hubspot";

        // Get failure email configuration from SSM
        const [failureMailTo, failureMailFrom, failureTemplateId] =
          await Promise.all([
            getConfigValue("MAIL_TO"),
            getConfigValue("MAIL_FROM"),
            getConfigValue("SENDGRID_FAILURE_EMAIL_TEMPLATE_ID"),
          ]);

        const failureEmail = await sendDataToSendGrid(
          failureMailTo,
          failureMailFrom,
          form_data?.emailAddress,
          failureTemplateId,
          formLeadData
        );

        // Send failure notification to Slack (webhook URL will be determined by sendToSlack)
        await sendToSlack(
          form_data,
          undefined, // Let sendToSlack determine the webhook URL
          "⚠️ HubSpot Form Submission Failed ⚠️"
        );

        if (failureEmail.status) {
          console.error(
            `${form_data?.secondary_source} form, failure email sent`
          );
        } else {
          console.error(
            `${form_data?.secondary_source} form, failed to send failure email`
          );
        }

        return {
          statusCode: hubspotResponse?.status || 500,
          headers: {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
            "Access-Control-Allow-Methods": "POST,OPTIONS"
          },
          body: JSON.stringify({
            message: "Form submission failed.",
            error: hubspotResponse?.error || "Unknown error from HubSpot",
          }),
        };
      }
    } catch (error) {
      console.error("Error sending to HubSpot:", error);
      return {
        statusCode: 500,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
          "Access-Control-Allow-Methods": "POST,OPTIONS"
        },
        body: JSON.stringify({
          message: "Internal server error while sending data to HubSpot",
          error: error.message || error,
        }),
      };
    }
  } catch (error) {
    console.error("Error parsing request:", error);
    return {
      statusCode: 400,
      headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
          "Access-Control-Allow-Methods": "POST,OPTIONS"
      },
      body: JSON.stringify({
        message: "Invalid request data",
        error: error.message || error,
      }),
    };
  }
};
